// 简单的测试脚本来验证内置数据源功能
// 运行方式: node test-built-in-sources.js

const fs = require('fs');
const path = require('path');

// 模拟 electron 环境
global.app = {
  getPath: () => './test-config'
};

// 确保测试目录存在
const testConfigDir = './test-config';
if (!fs.existsSync(testConfigDir)) {
  fs.mkdirSync(testConfigDir);
}

// 导入配置模块
const config = require('./dist-electron/config.js');

console.log('🧪 测试内置云端数据源功能\n');

// 测试1: 获取内置数据源
console.log('1️⃣ 测试获取内置数据源...');
try {
  const builtInSources = config.getBuiltInCloudSources();
  console.log(`✅ 成功获取 ${builtInSources.length} 个内置数据源:`);
  builtInSources.forEach((source, index) => {
    console.log(`   ${index + 1}. ${source.name} (${source.id})`);
    console.log(`      URL: ${source.url}`);
    console.log(`      启用: ${source.enabled}`);
    console.log(`      描述: ${source.description}`);
    console.log(`      内置: ${source.isBuiltIn}`);
    console.log('');
  });
} catch (error) {
  console.log('❌ 获取内置数据源失败:', error.message);
}

// 测试2: 配置文件初始化
console.log('2️⃣ 测试配置文件初始化...');
try {
  config.ensureConfigFile();
  console.log('✅ 配置文件初始化成功');
} catch (error) {
  console.log('❌ 配置文件初始化失败:', error.message);
}

// 测试3: 获取合并后的配置
console.log('3️⃣ 测试获取合并后的配置...');
try {
  const fullConfig = config.getConfig();
  console.log(`✅ 配置获取成功，包含 ${fullConfig.cloudKnowledgeSources.length} 个云端数据源`);
  
  const builtInCount = fullConfig.cloudKnowledgeSources.filter(s => s.isBuiltIn).length;
  const userCount = fullConfig.cloudKnowledgeSources.filter(s => !s.isBuiltIn).length;
  
  console.log(`   - 内置数据源: ${builtInCount} 个`);
  console.log(`   - 用户数据源: ${userCount} 个`);
} catch (error) {
  console.log('❌ 获取配置失败:', error.message);
}

// 测试4: 保存配置（应该过滤掉内置数据源）
console.log('4️⃣ 测试保存配置...');
try {
  const testConfig = config.getConfig();
  
  // 添加一个用户数据源
  testConfig.cloudKnowledgeSources.push({
    id: 'user-test-source',
    name: '用户测试数据源',
    url: 'https://example.com/test.json',
    enabled: true,
    isBuiltIn: false
  });
  
  const saveResult = config.saveConfig(testConfig);
  console.log(`✅ 配置保存${saveResult ? '成功' : '失败'}`);
  
  // 验证保存的配置文件内容
  const configPath = path.join(testConfigDir, 'config.json');
  if (fs.existsSync(configPath)) {
    const savedConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    const savedBuiltInCount = savedConfig.cloudKnowledgeSources.filter(s => s.isBuiltIn).length;
    const savedUserCount = savedConfig.cloudKnowledgeSources.filter(s => !s.isBuiltIn).length;
    
    console.log(`   - 保存的内置数据源: ${savedBuiltInCount} 个 (应该为0)`);
    console.log(`   - 保存的用户数据源: ${savedUserCount} 个`);
    
    if (savedBuiltInCount === 0) {
      console.log('✅ 内置数据源正确过滤，未保存到配置文件');
    } else {
      console.log('❌ 内置数据源未正确过滤');
    }
  }
} catch (error) {
  console.log('❌ 保存配置失败:', error.message);
}

// 测试5: 更新内置数据源同步时间
console.log('5️⃣ 测试更新内置数据源同步时间...');
try {
  const testTime = new Date().toLocaleString();
  config.updateBuiltInSourceSyncTime('builtin-tech-knowledge', testTime);
  
  const updatedSources = config.getBuiltInCloudSources();
  const techSource = updatedSources.find(s => s.id === 'builtin-tech-knowledge');
  
  if (techSource && techSource.lastSyncTime === testTime) {
    console.log('✅ 内置数据源同步时间更新成功');
    console.log(`   - 同步时间: ${techSource.lastSyncTime}`);
  } else {
    console.log('❌ 内置数据源同步时间更新失败');
  }
} catch (error) {
  console.log('❌ 更新同步时间失败:', error.message);
}

console.log('\n🎉 测试完成！');

// 清理测试文件
try {
  const configPath = path.join(testConfigDir, 'config.json');
  if (fs.existsSync(configPath)) {
    fs.unlinkSync(configPath);
  }
  fs.rmdirSync(testConfigDir);
  console.log('🧹 测试文件清理完成');
} catch (error) {
  console.log('⚠️ 清理测试文件时出错:', error.message);
}
