# 内置云端数据源功能

## 概述

系统现在支持内置的云端数据源，这些数据源在应用启动时会自动加载，并且具有以下特点：

- **不可编辑**：内置数据源的名称、URL等信息不能被用户修改
- **不可删除**：用户无法删除内置数据源
- **可启用/禁用**：用户可以控制是否启用某个内置数据源
- **自动同步**：内置数据源会参与自动同步过程
- **特殊标识**：在界面上会显示"内置"标签以区分用户自定义的数据源

## 技术实现

### 1. 配置文件结构

内置数据源定义在 `electron/config.ts` 中：

```typescript
const builtInCloudSources = [
  {
    id: 'builtin-tech-knowledge',
    name: '技术知识库',
    url: 'https://raw.githubusercontent.com/example/tech-knowledge/main/knowledge.json',
    enabled: true,
    isBuiltIn: true,
    description: '系统内置技术知识数据源',
  },
  // ... 更多内置数据源
]
```

### 2. 数据合并机制

- `getConfig()` 函数会自动将内置数据源与用户定义的数据源合并
- 内置数据源始终排在列表前面
- `saveConfig()` 函数只保存用户定义的数据源，过滤掉内置数据源

### 3. 同步时间管理

- 内置数据源的同步时间存储在内存中，不写入配置文件
- 使用 `updateBuiltInSourceSyncTime()` 函数更新内置数据源的同步时间
- 应用重启后，内置数据源的同步时间会重置

### 4. 界面交互

- 内置数据源在表格中显示"内置"标签
- 编辑和删除按钮对内置数据源会被禁用
- 测试连接功能对内置数据源正常工作
- 启用/禁用开关对内置数据源正常工作

## 如何添加新的内置数据源

1. 在 `electron/config.ts` 的 `builtInCloudSources` 数组中添加新的数据源配置
2. 确保设置 `isBuiltIn: true`
3. 提供唯一的 `id`、`name`、`url` 和 `description`
4. 设置默认的 `enabled` 状态

示例：

```typescript
{
  id: 'builtin-new-source',
  name: '新数据源',
  url: 'https://example.com/new-source.json',
  enabled: false,
  isBuiltIn: true,
  description: '新的内置数据源描述',
}
```

## 数据格式要求

内置数据源的URL应该返回以下格式之一的数据：

### JSON格式
```json
[
  {
    "author": "作者名称",
    "source": "来源",
    "content": "知识内容"
  }
]
```

或者：

```json
{
  "knowledgeBase": [
    {
      "author": "作者名称",
      "source": "来源", 
      "content": "知识内容"
    }
  ]
}
```

### CSV格式
```csv
作者,来源,内容
张三,技术文档,这是一条技术知识
李四,最佳实践,这是一条最佳实践
```

## 注意事项

1. **URL可访问性**：确保内置数据源的URL在目标环境中可以访问
2. **数据格式**：确保数据源返回的格式符合系统要求
3. **性能考虑**：内置数据源会在每次自动同步时被访问，考虑数据源的响应速度
4. **错误处理**：如果内置数据源无法访问，不会影响其他数据源的同步
5. **版本管理**：内置数据源的配置变更需要通过应用更新来分发

## 测试

可以通过以下方式测试内置数据源功能：

1. 启动应用，进入"云端同步设置"页面
2. 查看是否显示了内置数据源，并带有"内置"标签
3. 尝试编辑/删除内置数据源，应该被禁用
4. 测试内置数据源的连接功能
5. 启用内置数据源并执行同步
6. 检查知识库中是否包含来自内置数据源的数据
